@import "../../../../assets//scss/main.scss";

.subTitle {
  font-size: 1.4em !important;
  color: $primaryColor;
  margin: 0;
  padding: 0 1.2em;
  margin-top: 1em !important;
  @include for_media(mobileScreen) {
    color: $primaryColor;
    margin: 0 !important;
  }
}

.newTickettitle {
  background-color: #f6f8fa;
  color: $primaryColor !important;
  padding: 1em 0.5em;
  border-radius: 10px;
  margin: 0 1.2em;
}

.newTicketsList {
  margin: 0 1.5em;
  table {
    border-spacing: 0 15px !important;
    .ticketItem {
      border-radius: 20px !important;
      td {
        padding: 1em !important;
        p {
          font-size: 1em !important;
          font-weight: 900;
        }
      }
    }
  }
}
