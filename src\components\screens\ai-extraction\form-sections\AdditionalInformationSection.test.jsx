import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import AdditionalInformationSection from "./AdditionalInformationSection";
import { sectionValidation } from "../../../services/aiServices";
import { removeObjectsFromJson } from "../../../utils/jsonUtils";

jest.mock("../../../services/aiServices", () => ({
  sectionValidation: jest.fn(),
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    fileId: "mock-file-id",
    businessId: "mock-business-id",
  }),
}));

const mockFormData = {
  additional_information: {
    remarks: "Test Additional Info",
  },
};

let latestFormData = null;

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  latestFormData = formData;
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case "FIELD_CHANGE":
          updatedSection = { ...prevSection, [field]: value };
          break;

        case "UPDATE_SECTION":
          updatedSection = { ...prevSection, ...value };
          break;

        case "HARD_UPDATE_SECTION":
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <AdditionalInformationSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe("AdditionalInformationSection", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all fields with correct initial values", () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText("Remarks")).toHaveValue(
      "Test Additional Info"
    );
  });

  it("shows initial error messages & indicators correctly", async () => {
    const formDataWithErrors = {
      additional_information: {
        ...mockFormData.additional_information,
        error: {
          remarks: {
            short_message: "Invalid information",
            long_message: "Please enter valid additional information",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId("errorIcon");
    expect(errorIcons).toHaveLength(1);

    const infoTooltip = await screen.findByTestId("tooltip-remarks");
    await userEvent.hover(infoTooltip);
    const tooltip = await screen.findByText("Invalid information");
    expect(tooltip).toBeInTheDocument();
  });

  it("shows initial warning messages & indicators correctly", async () => {
    const formDataWithWarnings = {
      additional_information: {
        ...mockFormData.additional_information,
        warning: {
          remarks: {
            short_message: "Information format might be incorrect",
            long_message: "Please verify the additional information",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId("warningIcon");
    expect(warningIcons).toHaveLength(1);

    const infoTooltip = await screen.findByTestId("tooltip-remarks");
    await userEvent.hover(infoTooltip);
    const infoTooltipContent = await screen.findByText(
      "Information format might be incorrect"
    );
    expect(infoTooltipContent).toBeInTheDocument();
  });

  it("shows initial exact match indicators when present", async () => {
    const formDataWithSuccess = {
      additional_information: {
        ...mockFormData.additional_information,
        exact_match: {
          remarks: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId("successIcon");
    expect(successIcons).toHaveLength(1);
  });
});
