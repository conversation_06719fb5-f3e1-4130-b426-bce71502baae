import React, { useState } from "react";
import { Upload, File, Check, X } from "lucide-react";
import { Tooltip } from "react-tooltip";
import { toast } from "react-toastify";
import apiClient from "../../../services/apiClient";

const UploadBtn = ({
  uploadUrl,
  disabled = false,
  disableMessage = "Upload is disabled",
  accept = ".csv",
  uploadLabel = "Upload CSV",
  className = "",
  onSuccess,
  onError,
  refetchData,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(false);



  const handleOpenModal = () => !disabled && setIsModalOpen(true);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedFile(null);
    setUploadProgress(0);
    setError(null);
    setIsCompleted(false);
    setIsSuccessful(false);
  };

  const handleFinalizeUpload = () => {
    handleCloseModal();
    if (isSuccessful && refetchData) {
      refetchData();
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files?.[0]) {
      setSelectedFile(e.target.files[0]);
      setError(null);
      setIsCompleted(false);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !uploadUrl) return;

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setIsCompleted(false);
      setIsSuccessful(false);

      const formData = new FormData();
      formData.append("file", selectedFile);

      await apiClient.post(uploadUrl, formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(percentCompleted);
        },
      });

      setIsSuccessful(true);
      onSuccess?.();
    } catch (err) {
      const errorMsg =
        err.response?.data?.message || err.message || "Upload failed";
      setError(errorMsg);
      setIsSuccessful(false);
      toast.error(errorMsg);
      onError?.(err);
    } finally {
      setIsUploading(false);
      setIsCompleted(true);
    }
  };

  return (
    <>
      <button
        disabled={disabled}
        onClick={handleOpenModal}
        className={`flex items-center justify-center py-[0.9em] px-[0.8em] rounded-[1em] transition-colors duration-200 ${disabled
          ? "bg-gray-300 border-2 border-gray-400 text-gray-500 cursor-not-allowed opacity-70"
          : "common-btn-schema"
          } ${className}`}
        data-tooltip-id={disabled ? "tooltip" : undefined}
      >
        <Upload
          size={16}
          className={`mr-2 ${disabled ? "text-gray-500" : ""
            }`}
        />
        <span className="text-[1.2em] text-gray-800">{uploadLabel}</span>
      </button>

      {disabled && (
        <Tooltip
          id="tooltip"
          place="bottom"
          className="z-50 px-3 py-2 rounded-lg text-sm"
          style={{
            backgroundColor: "#011638",
            color: "#ffffff",
            fontWeight: 500,
          }}
        >
          {disableMessage}
        </Tooltip>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="px-6 py-4 border-b">
              <h3 className="text-lg font-medium">{uploadLabel}</h3>
            </div>

            <div className="p-6">
              {!isCompleted ? (
                <>
                  <div className="mb-4">
                    <label className="block mb-2">Select CSV File:</label>
                    <button
                      className={`flex items-center w-full px-4 py-2 border rounded-md transition-colors 
                         bg-accent1-bg border-white text-white"
                        }`}
                      onClick={() =>
                        document.getElementById("hidden-file-input").click()
                      }
                    >
                      <File size={18} className="mr-2 text-black" />
                      <span className="text-black">
                        {selectedFile ? selectedFile.name : "Choose File"}
                      </span>
                    </button>
                    <input
                      id="hidden-file-input"
                      type="file"
                      accept={accept}
                      hidden
                      onChange={handleFileChange}
                    />
                  </div>

                  {selectedFile && (
                    <div className="space-y-4">
                      <div className="text-sm">
                        <p>
                          <strong>File:</strong> {selectedFile.name}
                        </p>
                        <p>Size: {(selectedFile.size / 1024).toFixed(2)} KB</p>
                      </div>

                      {isUploading && (
                        <div className="pt-2">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Uploading...</span>
                            <span>{uploadProgress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className={`h-2.5 rounded-full transition-all duration-300 bg-[#011638]`}
                              style={{ width: `${uploadProgress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-4">
                  {isSuccessful ? (
                    <div className="flex flex-col items-center">
                      <div className="bg-green-100 p-3 rounded-full mb-4">
                        <Check size={32} className="text-green-600" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">
                        Upload Successful!
                      </h3>
                      <p className="text-gray-600">
                        Your file has been uploaded successfully.
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="bg-red-100 p-3 rounded-full mb-4">
                        <X size={32} className="text-red-600" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">
                        Upload Failed
                      </h3>
                      <p className="text-red-600">{error}</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="px-6 py-4 border-t flex justify-end space-x-3">
              {!isCompleted ? (
                <>
                  <button
                    onClick={handleCloseModal}
                    disabled={isUploading}
                    className="px-4 py-2 border-2 border-gray-400 rounded-md  text-gray-700 transition-colors disabled:opacity-50 bg-accent1-bg"
                  >
                    <span>Cancel</span>
                  </button>
                  <button
                    onClick={handleUpload}
                    disabled={!selectedFile || isUploading}
                    className={`px-4 py-2 rounded-md transition-colors ${!selectedFile || isUploading
                      ? "bg-gray-300 border-2 border-gray-400 text-gray-500 cursor-not-allowed opacity-70" :
                      "bg-accent1-bg border-2 border-white text-white cursor-pointer"
                      }`}
                  >
                    <span className="text-black">{isUploading ? "Uploading..." : "Upload"}</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={handleFinalizeUpload}
                  className={`px-4 py-2 rounded-md transition-colors 
                    bg-accent1-bg border-2 border-white text-white cursor-pointer"
                    }`}
                >
                  <span className="text-black">OK</span>
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UploadBtn;