import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ShipToDetailsSection from "./ShipToDetailsSection";

jest.mock("../../../services/aiServices", () => ({
  sectionValidation: jest.fn(),
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    fileId: "mock-file-id",
    businessId: "mock-business-id",
  }),
}));

const mockFormData = {
  ship_to_details: {
    party_name: "Parameshwara Jewellers",
    party_gst_no: "29ACPPB2436G1ZN",
    party_address: null,
    party_state_name: "Karnataka",
  },
};

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case "FIELD_CHANGE":
          updatedSection = { ...prevSection, [field]: value };
          break;

        case "UPDATE_SECTION":
          updatedSection = { ...prevSection, ...value };
          break;

        case "HARD_UPDATE_SECTION":
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <ShipToDetailsSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe("ShipToDetailsSection", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all fields with correct initial values", () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText("Party Name")).toHaveValue("Parameshwara Jewellers");
    expect(screen.getByLabelText("Party GST No.")).toHaveValue("29ACPPB2436G1ZN");
    expect(screen.getByLabelText("Party Address")).toHaveValue("");
    expect(screen.getByLabelText("State Name")).toHaveValue("Karnataka");
  });

  it("shows initial error messages & indicators correctly", async () => {
    const formDataWithErrors = {
      ship_to_details: {
        ...mockFormData.ship_to_details,
        error: {
          party_gst_no: {
            short_message: "Invalid GST number",
            long_message: "Please enter a valid GST number",
          },
          party_name: {
            short_message: "Invalid name",
            long_message: "Please enter a valid company name",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId("errorIcon");
    expect(errorIcons).toHaveLength(2);

    const gstTooltip = await screen.findByTestId("tooltip-party_gst_no");
    await userEvent.hover(gstTooltip);
    const tooltip = await screen.findByText("Invalid GST number");
    expect(tooltip).toBeInTheDocument();

    const nameTooltip = screen.getByTestId("tooltip-party_name");
    await userEvent.hover(nameTooltip);
    const nameTooltipContent = await screen.findByText("Invalid name");
    expect(nameTooltipContent).toBeInTheDocument();
  });

  it("shows initial warning messages & indicators correctly", async () => {
    const formDataWithWarnings = {
      ship_to_details: {
        ...mockFormData.ship_to_details,
        warning: {
          party_gst_no: {
            short_message: "GST number might be incorrect",
            long_message: "Please verify the GST number",
          },
          party_address: {
            short_message: "Address format might be incorrect",
            long_message: "Please verify the address format",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId("warningIcon");
    expect(warningIcons).toHaveLength(2);

    const gstTooltip = await screen.findByTestId("tooltip-party_gst_no");
    await userEvent.hover(gstTooltip);
    const gstTooltipContent = await screen.findByText(
      "GST number might be incorrect"
    );
    expect(gstTooltipContent).toBeInTheDocument();

    const addressTooltip = await screen.findByTestId("tooltip-party_address");
    await userEvent.hover(addressTooltip);
    const addressTooltipContent = await screen.findByText(
      "Address format might be incorrect"
    );
    expect(addressTooltipContent).toBeInTheDocument();
  });

  it("shows initial exact match indicators when present", async () => {
    const formDataWithSuccess = {
      ship_to_details: {
        ...mockFormData.ship_to_details,
        exact_match: {
          party_name: true,
          party_gst_no: true,
          party_address: true,
          party_state_name: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId("successIcon");
    expect(successIcons).toHaveLength(4);
  });
});
