export const resturls = {
  login: "/api/auth/login/",
  forgotPassword: "/api/auth/forgot-password/",
  verifyOtp: "/api/auth/verify-otp/",
  changePassword: "/api/auth/change-password/",
  resetPassword: "/api/auth/reset-password/",
  checkTokenValidity: "/api/auth/token-validation/",
  uploadFile: "/api/file/",
  getTeamList: "/api/business-users/",
  createTicket: "/api/ticket/",
  getBusinesses: "/api/get-businesses",
  getTickets: "/api/get-tickets/",
  addNewDocuments: "/api/document/",
  addComment: "/api/comment/",
  ticketList: "/api/ticket/",
  obtainCategortList: "/api/ticket-category/",
  obtainUsersList: "/api/business-users/",
  notificationRead: "/api/mark-notification-read/",
  obtainLogDownloadLink: "/api/logs/ticket-logs",
  obtainOverallDownloadLink: "/api/logs/ticket/business/",
  obtainCategoryWiseUser: "/api/get-all-types-users",
  createBusiness: "/api/business/",
  obtainBusinesses: "/api/business/",
  createUser: "/api/auth/generate-temp-creds/",
  obtainSubCategory: "/api/ticket-category/",
  file: "/api/file/",
  admin_stats: "/api/admin-stats",
  users: "/api/auth/user/",
  ticketTimelines: "/api/ticket-timelines",
  logEntry: "/api/log-entry",
  obtainTicketDownloadLink: "/api/ticket-timelines-download",
  obtainTimelineList: "api/ticket-timeline/",
  entryLogDownload: "/api/entry-logs-download",
  fetchBusinessType: "api/business-type/",
  fileFilter: "/api/file",
  filesDownload: "api/download-files-excel",
  profilefetch: "/api/auth/user/",
  emailserver: "/api/auth/smtp-config/",
  emailserverDownload: "/api/download-email-server-excel",
  userTicketStats: "/api/user-ticket-stats/",
  getCostReportData: "/api/v1/data_reporting/tally/cost_report",
  downloadCostReport: "/api/v1/data_reporting/tally/cost_report/download",
  obtainRevenueReportDetails: "api/v1/data_reporting/tally/revenue_report",
  obtainAccountReceivable: "api/v1/data_reporting/tally/account_receivable/",
  obtainCustomerContent: "api/v1/data_reporting/tally/account_receivable/customer_view",
  obtainInvoiceContent: "api/v1/data_reporting/tally/account_receivable/invoice_view",
  obtainDownloadReportUrl: "api/v1/data_reporting/tally/revenue_report/download",
  downloadInventoryReport: "api/v1/data_reporting/tally/inventory_report/download",
  downloadReceivableReport: "api/v1/data_reporting/tally/account_receivable/download",
  obtainLedgerList: "api/v1/data_reporting/tally/ledger",
  obtainLedgerBalanceInfo: "api/v1/data_reporting/tally/ledger/balance_report",
  obtainLedgerDetailedData: "api/v1/data_reporting/tally/ledger/transaction_report",
  obtainLedgerDownloadUrl: "api/v1/data_reporting/tally/ledger/transaction_report/download",
  getProjectedCashFlow: "api/v1/data_reporting/tally/projected_cash_flow",
  downloadProjectCashFlowReport: "api/v1/data_reporting/tally/projected_cash_flow/download",
  obtainAccountPayable: "api/v1/data_reporting/tally/account_payable/",
  obtainCustomerContentPayable: "api/v1/data_reporting/tally/account_payable/customer_view",
  obtainInvoiceContentPayable: "api/v1/data_reporting/tally/account_payable/invoice_view",
  downloadPayableReport: "api/v1/data_reporting/tally/account_payable/download",
  getInventoryReportData: "api/v1/data_reporting/tally/inventory_report/",
  extractInvoice: "api/v1/ai_invoice/invoice/analyze_invoice",
  extractedInvoiceRootDomain: "api/v1/ai_invoice/invoice/",
  validateGST: "/api/v1/ai_invoice/invoice/validate_gst",
  downloadInvoices: "/api/v1/ai_invoice/invoice/accounting_payload/download",
  getGstLedgers: "/api/v1/ai_invoice/tax_ledger_mapping",
  exportInvoice: "/api/v1/ai_invoice/invoice/accounting_payload/download",
  getPurchaseLedger: "/api/v1/ai_invoice/ledgers",
  getStockItems: "api/v1/ai_invoice/items",
  getCostCenter: "api/v1/ai_invoice/cost_centers",
  changeInvoiceStatus: "api/v1/ai_invoice/invoice/status",
  syncMail: "/api/sync-emails/",
  stockItems: `api/v1/ai_invoice/items`,
  stockItemsUpload: `api/v1/data_collection/file/stock_items/upload`,
  stockItemsDownload: `api/v1/ai_invoice/items/download`,

  NonstockItems: `api/v1/ai_invoice/non_stock_item_mapping`,
  NonstockItemsUpload: `api/v1/ai_invoice/non_stock_item_mapping/upload`,
  NonstockItemsDownload: `api/v1/ai_invoice/non_stock_item_mapping/download`,

  CostcentrsItems: `api/v1/ai_invoice/cost_centers`,
  CostcentrsItemsUpload: `api/v1/ai_invoice/cost_centers/upload`,
  CostcentrsItemsDownload: `api/v1/ai_invoice/cost_centers/download`,

  TaxledgersItems: `api/v1/ai_invoice/tax_ledger_mapping`,
  TaxledgersItemsUpload: `api/v1/ai_invoice/tax_ledger_mapping/upload`,
  TaxledgersItemsDownload: `api/v1/ai_invoice/tax_ledger_mapping/download`,

  StockItemsMapping: `api/v1/ai_invoice/stock_items_mapping`,
  StockItemsMappingUpload: `api/v1/ai_invoice/stock_items_mapping/upload`,
  StockItemsMappingDownload: `api/v1/ai_invoice/stock_items_mapping/download`,
  leadgertypemapping: `api/v1/ai_invoice/ledgers/mapping`,
  leadgertypemappingUpload: `api/v1/ai_invoice/ledgers/mapping/upload`,
  leadgertypemappingDownload: `api/v1/ai_invoice/ledgers/mapping/download`,

  leadgerscreenitems: `api/v1/ai_invoice/ledgers`,
  leadgerscreenitemsUpload: `api/v1/data_collection/file/ledger/upload`,
  leadgerscreenitemsDownload: `api/v1/ai_invoice/ledgers/download`,

  leadgergroupscreenitems: `api/v1/ai_invoice/groups`,
  leadgergroupscreenitemsUpload: `api/v1/data_collection/file/group/upload`,
  leadgergroupscreenitemsDownload: `api/v1/ai_invoice/groups/download`,

};
