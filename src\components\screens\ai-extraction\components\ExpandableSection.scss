@import "../../../../assets/scss/main.scss";

.expandable-section {
  overflow: hidden;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 4em;
    padding: 0 1.25em;
    @include clickable;
    border-bottom: 3px solid $borderColor;
    .section-title {
      font-family: "DM Sans", sans-serif;
      font-weight: 600;
      font-size: 1.2em;
      color: #181d27;
      margin: 0;
    }

    .status-indicator {
      @include flex-center;
      gap: 0.2em;
      font-size: 1em;
      font-weight: 500;
      padding: 0.2em 0.4em;
      border-radius: 1em;
      &.error {
        background-color: rgba(240, 68, 56, 0.1);
        color: #f04438;
      }

      &.warning {
        background-color: rgba(247, 144, 9, 0.1);
        color: #f79009;
      }

      &.missing {
        background-color: rgba(247, 144, 9, 0.1);
        color: #f79009;
        font-size: 0.85em;
      }
    }

    .chevron-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 300ms ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .section-content-wrapper {
    will-change: max-height, opacity;
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transition: max-height 300ms ease-in-out, opacity 200ms ease-in-out;
    .section-content {
      padding: 1em;
      padding-bottom: 0.5em;
    }
  }
}
