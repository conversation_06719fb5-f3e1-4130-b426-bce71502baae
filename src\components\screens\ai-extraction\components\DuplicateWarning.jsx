import React, { useState } from "react";
import { AlertTriangle, ChevronDown, X } from "lucide-react";
import DuplicateInfoTable from "./DuplicateInfoTable";

function DuplicateWarning({ duplicateData = [], duplicateType = null }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return <></>;
  return (
    <div className="mt-5 mb-3 mx-3 border-2 border-gray-200 rounded-lg shadow-sm overflow-hidden">
      {/* Header */}
      <div
        className="px-4 py-2 bg-white cursor-pointer select-none"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className={`p-1.5 rounded-full flex-shrink-0 ${
                duplicateType === "confirmed" ? "bg-red-100" : "bg-yellow-100"
              }`}
            >
              <AlertTriangle
                className={`h-7 w-7 ${
                  duplicateType === "confirmed"
                    ? "text-red-500"
                    : "text-yellow-600"
                }`}
                strokeWidth={2}
              />
            </div>
            <h2 className="text-lg font-semibold m-0">
              Duplicate Invoice Warning
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <div
              className="flex items-center justify-center bg-gray-100 p-1 rounded-full hover:bg-gray-300 transition-colors"
              aria-label={
                isExpanded
                  ? "Collapse duplicate details"
                  : "Expand duplicate details"
              }
            >
              <ChevronDown
                className={`h-6 w-6 flex-shrink-0 text-gray-600 transition-transform duration-500 ${
                  isExpanded ? "rotate-180" : ""
                }`}
              />
            </div>
            <div
              className="flex items-center justify-center bg-gray-100 p-1 rounded-full hover:bg-gray-300 transition-colors"
              onClick={() => setIsVisible(false)}
              aria-label="Close duplicate warning"
            >
              <X className="h-6 w-6 flex-shrink-0 text-gray-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Content */}
      <div
        className={`transition-all duration-500 ease-in-out overflow-y-auto ${
          isExpanded ? "max-h-[470px] opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <DuplicateInfoTable data={duplicateData} />
      </div>
    </div>
  );
}

export default DuplicateWarning;
