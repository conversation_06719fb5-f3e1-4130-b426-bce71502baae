import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import { getYear, getMonth, format } from "date-fns";
import style from "./customDatePicker.module.scss";
import { range } from "lodash";
import { getValidDate } from "../../utils/dateUtils";

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const years = range(1990, getYear(new Date()) + 1, 1);

function CustomDatePicker({
  selected,
  name,
  className = "",
  dateFormat = "dd/MM/yyyy",
  onDateChange = null,
  disabled = false,
  portalId,
  id = "",
}) {
  const [internalStartDate, setInternalStartDate] = useState(
    selected || new Date()
  );
  const startDate =
    selected === undefined
      ? internalStartDate
      : getValidDate(selected, dateFormat);

  useEffect(() => {
    if (selected !== undefined) {
      const parsedDate = getValidDate(selected, dateFormat);
      setInternalStartDate(parsedDate);
    }
  }, [selected, dateFormat]);

  const handleDateChange = (date) => {
    if (disabled) return;

    if (selected === undefined) {
      setInternalStartDate(date);
    }
    if (onDateChange && date) {
      try {
        const formattedDate = format(date, dateFormat);
        onDateChange(formattedDate);
      } catch (error) {
        console.error("Error formatting date:", error);
        onDateChange(date);
      }
    }
  };

  return (
    <div className={`${style.fieldWrapper} ${className} ${disabled && "opacity-40 cursor-not-allowed"}`}>
      <DatePicker
        id={id}
        renderCustomHeader={({
          date,
          changeYear,
          changeMonth,
          decreaseMonth,
          increaseMonth,
          prevMonthButtonDisabled,
          nextMonthButtonDisabled,
        }) => (
          <div className={style.customHeaderContainer}>
            <button
              className={style.navButton}
              onClick={decreaseMonth}
              disabled={prevMonthButtonDisabled || disabled}
              aria-label="Previous Month"
            >
              {"🡠"}
            </button>

            <div className={style.selectsContainer}>
              <select
                className={style.monthSelect}
                value={months[getMonth(date)]}
                onChange={({ target: { value } }) =>
                  changeMonth(months.indexOf(value))
                }
                aria-label="Select Month"
                disabled={disabled}
              >
                {months.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>

              <select
                className={style.yearSelect}
                value={getYear(date)}
                onChange={({ target: { value } }) => changeYear(value)}
                aria-label="Select Year"
                disabled={disabled}
              >
                {years.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>

            <button
              className={style.navButton}
              onClick={increaseMonth}
              disabled={nextMonthButtonDisabled || disabled}
              aria-label="Next Month"
            >
              {"🡢"}
            </button>
          </div>
        )}
        selected={startDate}
        onChange={handleDateChange}
        dateFormat={dateFormat}
        portalId={portalId}
        disabled={disabled}
      />
      <input type="hidden" name={name} value={startDate || ""} />
    </div>
  );
}

export default React.memo(CustomDatePicker);
