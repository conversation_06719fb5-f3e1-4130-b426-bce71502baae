import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import OtherInformationSection from "./OtherInformationSection";

jest.mock("../../../services/aiServices", () => ({
  sectionValidation: jest.fn(),
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    fileId: "mock-file-id",
    businessId: "mock-business-id",
  }),
}));

const mockFormData = {
  other_information: {
    payment_terms: "0",
    e_way_bill_details: null,
    e_way_bill_details_date: null,
  },
};

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case "FIELD_CHANGE":
          updatedSection = { ...prevSection, [field]: value };
          break;

        case "UPDATE_SECTION":
          updatedSection = { ...prevSection, ...value };
          break;

        case "HARD_UPDATE_SECTION":
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <OtherInformationSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe("OtherInformationSection", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all fields with correct initial values", () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText("Payment Terms (In days)")).toHaveValue("0");
    expect(screen.getByLabelText("E-Way Bill Details")).toHaveValue("");
    expect(screen.getByLabelText("E-Way Bill Date (dd/mm/yyyy)")).toHaveValue(
      ""
    );
  });

  it("shows initial error messages & indicators correctly", async () => {
    const formDataWithErrors = {
      other_information: {
        ...mockFormData.other_information,
        error: {
          payment_terms: {
            short_message: "Invalid payment terms",
            long_message: "Please enter valid payment terms",
          },
          e_way_bill_details: {
            short_message: "Invalid e-way bill details",
            long_message: "Please enter valid e-way bill details",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId("errorIcon");
    expect(errorIcons).toHaveLength(2);

    const paymentTermsTooltip = await screen.findByTestId(
      "tooltip-payment_terms"
    );
    await userEvent.hover(paymentTermsTooltip);
    const paymentTermsTooltipContent = await screen.findByText(
      "Invalid payment terms"
    );
    expect(paymentTermsTooltipContent).toBeInTheDocument();

    const ewayBillTooltip = screen.getByTestId("tooltip-e_way_bill_details");
    await userEvent.hover(ewayBillTooltip);
    const ewayBillTooltipContent = await screen.findByText(
      "Invalid e-way bill details"
    );
    expect(ewayBillTooltipContent).toBeInTheDocument();
  });

  it("shows initial warning messages & indicators correctly", async () => {
    const formDataWithWarnings = {
      other_information: {
        ...mockFormData.other_information,
        warning: {
          payment_terms: {
            short_message: "Payment terms might be incorrect",
            long_message: "Please verify the payment terms",
          },
          e_way_bill_details: {
            short_message: "E-way bill details might be incorrect",
            long_message: "Please verify the e-way bill details",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId("warningIcon");
    expect(warningIcons).toHaveLength(2);

    const paymentTermsTooltip = await screen.findByTestId(
      "tooltip-payment_terms"
    );
    await userEvent.hover(paymentTermsTooltip);
    const paymentTermsTooltipContent = await screen.findByText(
      "Payment terms might be incorrect"
    );
    expect(paymentTermsTooltipContent).toBeInTheDocument();

    const ewayBillTooltip = await screen.findByTestId(
      "tooltip-e_way_bill_details"
    );
    await userEvent.hover(ewayBillTooltip);
    const ewayBillTooltipContent = await screen.findByText(
      "E-way bill details might be incorrect"
    );
    expect(ewayBillTooltipContent).toBeInTheDocument();
  });

  it("shows initial exact match indicators when present", async () => {
    const formDataWithSuccess = {
      other_information: {
        ...mockFormData.other_information,
        exact_match: {
          payment_terms: true,
          e_way_bill_details: true,
          e_way_bill_date: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId("successIcon");
    expect(successIcons).toHaveLength(3);
  });
});
