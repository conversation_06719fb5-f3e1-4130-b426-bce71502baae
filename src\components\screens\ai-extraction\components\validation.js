/**
 * Utility functions for AiForm validation
 */

import { checkSameState } from "../../../utils/aiUtils";
import { isEmpty } from "../../../utils/helperUtils";

// Define validation rules for each section
export const VALIDATION_RULES = {
  supplier_details: {
    requiredFields: ["supplier_name"],
    conditionalFields: [],
  },
  bill_to_details: {
    requiredFields: ["invoice_no", "invoice_date", "buyer_name"],
    conditionalFields: [],
  },
  invoice_summary: {
    requiredFields: ["invoice_amount"],
    conditionalFields: [
      {
        field: "frieght_charges_ledger_name",
        condition: (data) =>
          data.freight_charges && Number(data.freight_charges) !== 0,
      },
      {
        field: "insurance_charges_ledger_name",
        condition: (data) =>
          data.insurance_charges && Number(data.insurance_charges) !== 0,
      },
      {
        field: "tcs_ledger_name",
        condition: (data) => data.tcs && Number(data.tcs) !== 0,
      },
      {
        field: "other_charges_ledger_name",
        condition: (data) =>
          data.other_charges && Number(data.other_charges) !== 0,
      },
      {
        field: "discount_ledger_name",
        condition: (data) => data.discount && Number(data.discount) !== 0,
      },
      {
        field: "round_off_ledger_name",
        condition: (data) => data.round_off && Number(data.round_off) !== 0,
      },
    ],
  },
  //Empty but still requires for reference
  sales_of_product_services: {
    requiredFields: [],
    conditionalFields: [],
  },
  gst_ledgers: {
    requiredFields: [],
    conditionalFields: [],
  },
};

/**
 * Validates if all required fields are filled in a section
 * @param {Object} formData - The form data
 * @returns {Array} - Array of section keys with missing required fields
 */
export function validateRequiredFields(formData) {
  const missingSections = [];
  const sections = Object.keys(VALIDATION_RULES);

  sections.forEach((sectionKey) => {
    const sectionData = formData[sectionKey];
    if (!sectionData) {
      // If section data is missing but has required fields, mark as missing
      if (VALIDATION_RULES[sectionKey].requiredFields.length > 0) {
        missingSections.push(sectionKey);
      }
      return;
    }

    // Handle special cases
    if (sectionKey === "sales_of_product_services") {
      if (validateProductServicesSection(formData, sectionData)) {
        missingSections.push(sectionKey);
      }
      return;
    }

    if (sectionKey === "gst_ledgers") {
      if (validateGstLedgersSection(formData, sectionData)) {
        missingSections.push(sectionKey);
      }
      return;
    }

    // Standard validation for other sections
    const rules = VALIDATION_RULES[sectionKey];

    // Check required fields
    const hasMissingRequiredFields = rules.requiredFields.some(
      (field) => !sectionData[field]
    );

    // Check conditional fields
    const hasMissingConditionalFields = rules.conditionalFields.some(
      ({ field, condition }) => condition(sectionData) && !sectionData[field]
    );

    if (hasMissingRequiredFields || hasMissingConditionalFields) {
      missingSections.push(sectionKey);
    }
  });

  return missingSections;
}

/**
 * Validates for required fields in the Product Services section
 * @param {Object} formData - The complete form data
 * @param {Array} sectionData - The section data array
 * @returns {Boolean} - True if validation fails (has missing fields)
 */
function validateProductServicesSection(formData, sectionData) {
  if (!Array.isArray(sectionData) || sectionData.length === 0) {
    return true;
  }

  // Check for missing item_name or amount
  const hasMissingBasicFields = sectionData.some(
    (item) => !item.item_name || !item.amount
  );

  const invoiceSummary = formData["invoice_summary"] || {};
  const missingInvoiceSummaryField =
    !invoiceSummary?.currency || isEmpty(invoiceSummary?.exchange_rate);

  // Check for missing ledger fields based on conditions
  let hasMissingLedger = false;
  const purchaseLedgerMode = formData?.purchase_ledger_mode?.toLowerCase();
  const invoiceType = formData?.invoice_type;
  const billToDetails = formData["bill_to_details"] || {};

  if (purchaseLedgerMode === "invoice" && invoiceType !== "expense") {
    // Check bill_to_details purchase_ledger_name
    if (!billToDetails?.purchase_ledger_name) {
      hasMissingLedger = true;
    }
  } else if (purchaseLedgerMode === "item" || invoiceType === "expense") {
    // Check item-level purchase_ledger_name
    hasMissingLedger = sectionData.some((item) => !item.purchase_ledger_name);
  }

  return (
    hasMissingBasicFields || hasMissingLedger || missingInvoiceSummaryField
  );
}

/**
 * Validates for required fields in the GST Ledgers section
 * @param {Object} formData - The complete form data
 * @param {Array} sectionData - The section data array
 * @returns {Boolean} - True if validation fails (has missing fields)
 */
function validateGstLedgersSection(formData, sectionData) {
  if (!Array.isArray(sectionData) || sectionData.length === 0) {
    return false;
  }

  // Check for missing ledger fields for non-zero GST amounts
  const isSameState = checkSameState(formData);

  // Zoho Validation
  const hasMissingGstLedger = sectionData.some((item) => {
    if (formData?.accounting_platform?.toLowerCase() === "zoho") {
      return isSameState
        ? !item?.zoho_tax_group_ledger_name
        : !item?.zoho_tax_ledger_name;
    }

    // Tally/Busy Validation
    if (isSameState) {
      // Check CGST and SGST ledgers
      return (
        (Number(item?.cgst_amount) !== 0 && !item?.cgst_ledger_name) ||
        (Number(item?.sgst_amount) !== 0 && !item?.sgst_ledger_name)
      );
    } else {
      // Check IGST ledger
      return Number(item?.igst_amount) !== 0 && !item?.igst_ledger_name;
    }
  });
  // Check CESS ledger
  const otherTaxLedgers = formData?.other_tax_ledgers || {};
  const hasMissingCessLedger =
    otherTaxLedgers?.cess_amount &&
    Number(otherTaxLedgers?.cess_amount) !== 0 &&
    !otherTaxLedgers?.cess_ledger_name;

  return hasMissingGstLedger || hasMissingCessLedger;
}
