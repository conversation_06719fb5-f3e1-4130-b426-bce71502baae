@import "../../../assets//scss/main.scss";

.menuListContainer{
    // width: 20%;
    min-width: 257px;
    padding: 1em 0em 0em 0;
    background-color: $white;
    min-height: 100%;
    max-height: 100vh;
    height: 89vh;
    border-radius: 15px;
    box-shadow: 0 0 1px 1px $borderColor;
    position: relative;
    overflow-y: auto;
    @include hide-scrollbar;
}
.selectedBusiness{
    display: flex;
    gap: 1em;
    align-items: center;
    justify-content: space-between;
    padding: 0.5em;
    background-color: $secondaryBgColor;
    color: $primaryColor;
    border-radius: 15px;
    border: 2px solid $accentBgColor1;
    margin: 1em;
    margin-top: 0;
    font-size: .9em;
    @include clickable;
    .logoWrapper{
        padding: 0.5em;
        box-shadow: 0 0 1px 2px $borderColor;
        border-radius: 10px;
        background-color: $white;
        flex-shrink: 0;
        img, svg{
            width: 25px;
            height: 25px;
        }
    }
    p{
        margin: 0;
    }
    svg{
        width: 25px;
        height: 25px;
        cursor: pointer;
    }
};
.recordBtn{
    display: flex;
    gap: 1em;
    justify-content: center;
    align-items: center;
    height: 2.6em;
    margin: 0 2em;
    font-size: 1rem;
    background-color: $accentBgColor1;
    color: $primaryColor;
    font-weight: 900;
    border-radius: 35px;
    cursor: pointer;
    padding: 0 2em; 
    transition: all;
    transition-duration: 300ms;
    svg{
        height: 25px;
        width: 25px;
        stroke: $primaryColor !important;
    }
    &:hover{
        background-color: $accentHover1;
    }
}

.menuTitle{
    display: flex;
    align-items: center;
    gap: 1em;
}

.menuList{
    margin-top: 1.2em !important;
    overflow-y: visible;
    ol{
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 1em;
        padding: 0;
        li{
            display: flex;
            gap: 1em;
            align-items: center;
            font-size: 1.2em;
            padding: 1em 2em;
            svg{
                width: 30px;
                height: 30px;
            }
            cursor: pointer;
            position: relative;
        }
    }
    .activeMenu{
        position: relative;
    }
    .activeMenu::after{
        content: "";
        position: absolute;
        height: 100%;
        width: 100%;
        left: 0;
        background: linear-gradient(to left, #a1e0ff88, transparent);
    }
    .activeMenu::before{
        content: "";
        position: absolute;
        height: 100%;
        width: 5px;
        right: 0;  
        background-color: $accentColor1;
    }
    .menuItemIcon{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }
    .menuItemIcon svg{
        margin-left: auto;
    }

}

.businessListContainer{
    height: 100%;
    width: 100%;
    // min-width: 257px;
    background-color: $white;
    border-radius: 20px;
    position: absolute;
    top: 0;
    z-index: -1;
    left: 0;
    transition: all 1s;
    padding: 1em;
    opacity: 0;

    .menuItem{
        display: flex;
        gap: 1em;
        align-items: center !important;
        padding: 1em;
        background-color: $white;
        border-radius: 1em;
        @include clickable;
        p{
            font-size: 1.2em;
        }
    }
    .logoWrapper{
        padding: 0.5em;
        box-shadow: 0 0 1px 2px $borderColor;
        border-radius: 10px;
        background-color: $white;
        flex-shrink: 0;
        img, svg{
            width: 30px;
            height: 30px;
        }
    }
    .activeMenu{
        background-color: $accentBgColor1;
        box-shadow: 0 0 0px 1px $accentBorder1;
        p{
           -webkit-text-stroke: 0.1px;
        }
    }
}

.activeContent{
    left: 105%;
    z-index: 1000;
    opacity: 1;
    max-height: 85vh;
    max-height: 85dvh;
    overflow-y: auto;
}

.adminContainer{
    .selectedBusiness{
        .orgName{
            font-weight: 900;
            color: $white !important;
        }
        .desc{
            font-size: 1em !important;
            color: $blueBackGround;
        }
    }
    svg{
        width: 35px;
        height: 35px;
    }
    .activeMenu{
        svg, path{
            fill: $accentColor1;
            // stroke: #4E5BA6;
        }
    }
    .activeMenu::after{
        content: "";
        position: absolute;
        height: 100%;
        width: 100%;
        left: 0;
        background: linear-gradient(to left, #a1e0ff88, transparent);
    }
    .activeMenu::before{
        content: "";
        position: absolute;
        height: 100%;
        width: 5px;
        right: 0;        
        background-color: $accentColor1;
        z-index: 10;
    }
    li{
        padding: 1em 2em !important;
    }
}

.strokeType{
    svg, path{
        fill: white !important;
        stroke: #4E5BA6;
    }
}

.selectedBusinessName {
    position: relative;
    font-size: 1.1em;
    width: 100%;
    height: 3.5em;
    white-space: wrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.accordion{
    width: 100%;
}
.accordMenu{
    display: flex;
    align-items: center;
    justify-content: space-between;
    div{
        display: flex;
        align-items: center;
        gap: 1em;
        svg{
            height: 30px;
            width: 30px;
        }
    }
}
.subMenuHeader{
    padding: 0 !important;
    .accordTitle{
        padding: 1em 2em !important;
    }
}

.accordContent{
    max-height: 300px;
    overflow-y: auto;
    li{
        padding-left: 5em !important;
        font-size: 1em !important;
    }
}

.additionMenu{
    position: sticky;
    bottom: 0;
    background-color: $white;
    margin-top: auto;
    padding-top: 1em;
    border-top: 1px solid #f0f0f0;
    li{
        padding: 0.5em 2em !important;
    }
}

.activeDropdown{
    transform: rotate(180deg);
}

.dropdownIcon{
    transition: all 0.5s;
    svg{
        width: 25px !important;
        height: 25px !important;
    }
}

.mainWrapper{
    position: relative;
}