import { useCallback, useState } from "react";

function useDragAndDrop(onDropFiles) {
  const [isDragActive, setIsDragActive] = useState(false);

  const preventDefaults = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrag = useCallback(
    (e, isEntering) => {
      preventDefaults(e);
      setIsDragActive(isEntering);
    },
    [preventDefaults]
  );

  const handleDrop = useCallback(
    (e) => {
      preventDefaults(e);
      setIsDragActive(false);

      const droppedFiles = Array.from(e.dataTransfer.files || []);
      if (droppedFiles.length > 0) {
        onDropFiles(droppedFiles);
      }
    },
    [onDropFiles, preventDefaults]
  );

  return {
    isDragActive,
    eventHandlers: {
      onDragEnter: (e) => handleDrag(e, true),
      onDragLeave: (e) => handleDrag(e, false),
      onDragOver: preventDefaults,
      onDrop: handleDrop,
    },
  };
}

export default useDragAndDrop;
