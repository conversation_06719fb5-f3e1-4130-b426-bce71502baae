import React from "react";
import { jsonKeyFormatLabel } from "../../../utils";
import { Tooltip } from "react-tooltip";
import { AlertCircle, AlertTriangle } from "lucide-react";

function scrollToId(id) {
  const container = document.getElementById("ai-extraction-form-container");
  const element = document.getElementById(id);
  const header = document.getElementById("form-header");
  const headerHeight = header?.getBoundingClientRect().height || 0;

  if (element && container) {
    const headerOffset = headerHeight - 40;
    const elementTop = element.offsetTop;
    const scrollTop = elementTop - headerOffset;

    container.scrollTo({
      top: scrollTop,
      behavior: "smooth",
    });
  }
}

function ValidationTooltip({
  sectionsWithErrors = [],
  sectionsWithMissingFields = [],
  transformSectionName = {},
}) {
  return (
    <Tooltip
      anchorSelect="#validate-button"
      place="top"
      style={{
        background: "#FFFFFF",
        boxShadow:
          "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        borderRadius: "6px",
        padding: "0",
        minWidth: "20em",
      }}
      clickable
    >
      <div className="p-4 max-w-xs">
        <div className="flex items-center mb-3 border-b pb-2">
          <AlertTriangle className="text-amber-500 mr-2" />
          <h4 className="text-nowrap text-amber-500 font-semibold m-0">
            Validation Issues
          </h4>
        </div>

        <ul className="space-y-3">
          <li>
            <SectionAlert
              label="Errors in:"
              alertList={sectionsWithErrors}
              colorSchema={{
                iconColor: "text-red-500",
                textColor: "text-red-600",
                bgColor: "bg-red-50",
                tagTextColor: "text-red-700",
                tagHoverBgColor: "bg-red-100",
              }}
              icon={<AlertCircle />}
            />
          </li>

          <li>
            <SectionAlert
              label="Missing required fields in:"
              alertList={sectionsWithMissingFields}
              colorSchema={{
                iconColor: "text-amber-500",
                textColor: "text-amber-600",
                bgColor: "bg-amber-50",
                tagTextColor: "text-amber-700",
                tagHoverBgColor: "bg-amber-100",
              }}
              icon={<AlertTriangle />}
              transformSectionName={transformSectionName}
            />
          </li>
        </ul>
      </div>
    </Tooltip>
  );
}

function SectionAlert({
  label,
  alertList = [],
  colorSchema = {
    iconColor: "text-red-500",
    textColor: "text-red-600",
    bgColor: "bg-red-50",
    tagTextColor: "text-red-700",
    tagHoverBgColor: "bg-red-100",
  },
  icon = <AlertCircle />,
  transformSectionName,
}) {
  if (!alertList || alertList.length === 0) return <></>;

  return (
    <div className="flex flex-col items-start gap-3">
      <div className="flex items-center">
        {React.cloneElement(icon, {
          className: `w-5 h-5 ${colorSchema.iconColor} mr-2 flex-shrink-0`,
        })}
        <span
          className={`${colorSchema.textColor}  text-nowrap font-medium text-sm`}
        >
          {label}
        </span>
      </div>
      <div className="flex flex-wrap gap-1">
        {alertList.map((section) => (
          <span
            key={section}
            onClick={() => scrollToId(section)}
            className={`cursor-pointer ${colorSchema.bgColor} ${colorSchema.tagTextColor} px-2 py-1 rounded-md text-xs hover:${colorSchema.tagHoverBgColor} transition-colors`}
            title={`Navigate to ${jsonKeyFormatLabel(section)}`}
          >
            {transformSectionName &&
            Object.keys(transformSectionName).includes(section)
              ? transformSectionName[section]
              : jsonKeyFormatLabel(section)}
          </span>
        ))}
      </div>
    </div>
  );
}

export default ValidationTooltip;
