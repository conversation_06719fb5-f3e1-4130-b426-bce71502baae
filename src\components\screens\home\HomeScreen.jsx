import React from "react";
import style from "./homeScreen.module.scss";
import AdminHome from "./admin/AdminHome";
import { useAuth } from "../../../contexts/AuthContext";
import UserHome from "./user/UserHome";
import AccountantHome from "./accountant/AccountantHome";
import LayoutWrapper from "../../generic-components/LayoutWrapper";

function HomeScreen() {
  const { role, userProfile } = useAuth();
  return (
    <LayoutWrapper className={style.homeScreenBg}>
      <div className={style.homeScreenContainer}>
        <p className={style.welcomeMsg}>
          Welcome, {`${userProfile?.full_name}` || "-"} !
        </p>

        {role === "superuser" && <AdminHome />}
        {["business_user", "business_superuser"].includes(role) && (
          <UserHome role={role} />
        )}
        {["manager", "accountant"].includes(role) && <AccountantHome />}
      </div>
    </LayoutWrapper>
  );
}

export default HomeScreen;
