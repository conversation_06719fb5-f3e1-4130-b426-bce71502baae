import React, { useState } from "react";
import { Modal, Image as SemanticImage } from "semantic-ui-react";
import { X, RotateCcw, Loader2 } from "lucide-react";
import { getNonePreviewableFileTypePngIcon } from "../utils";

/**
 * @typedef {Object} FilePreview
 * @property {string} name - File name.
 * @property {string} previewUrl - URL for previewing the file.
 * @property {number} progress - Upload progress percentage (0–100).
 * @property {'uploading' | 'success' | 'failed'} status - Upload status.
 * @property {string|null} id - Unique client-side ID (nullable).
 * @property {string|null} responseId - Server response ID (nullable).
 */

/**
 * File preview component.
 *
 * @param {Object} props - Component props.
 * @param {FilePreview} props.file - File preview object.
 * @param {Function} props.onRemove - Callback to remove the file.
 * @param {Function} props.onRetry - Callback to retry the upload.
 */
const FilePreview2 = ({ file, onRemove, onRetry }) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const isNonePreviewable = getNonePreviewableFileTypePngIcon(file.name);

  return (
    <>
      <div className="flex items-center w-full border border-gray-200 gap-3 p-3 rounded-xl my-2 bg-white relative shadow-sm">
        {/* Thumbnail */}
        <div
          onClick={() => setIsPreviewOpen(!!file.previewUrl)}
          className="w-14 h-14 flex-shrink-0 flex items-center justify-center overflow-hidden rounded-lg cursor-pointer border border-gray-200 bg-gray-50"
          tabIndex={0}
          aria-label={`Preview ${file.name}`}
          role="button"
        >
          <img
            src={isNonePreviewable ? isNonePreviewable.icon : file.previewUrl}
            alt={file.name}
            className="object-contain w-full h-full"
          />
        </div>

        {/* File Info & Progress */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <p
              className="flex-1 text-base font-medium text-gray-800 truncate"
              title={file.name}
            >
              {file.name}
            </p>
            {/* Remove button (success only) */}
            {file.status !== "uploading" && (
              <button
                onClick={onRemove}
                className="p-1.5 rounded-full ml-2 bg-accent2 hover:bg-accent2-hover text-white focus:outline-none"
                title="Remove"
                aria-label="Remove file"
                type="button"
              >
                <X className="h-5 w-5" />
              </button>
            )}
            {/* Retry button (error only) */}
            {file.status === "error" && (
              <button
                onClick={onRetry}
                className="p-1.5 rounded-full ml-2 text-error bg-error-bg hover:bg-error-bg-hover focus:outline-none"
                title="Retry"
                aria-label="Retry upload"
                type="button"
              >
                <RotateCcw className="h-5 w-5" />
              </button>
            )}
          </div>

          {/* Progress bar and status */}
          {file.status === "uploading" && (
            <div className="mt-2">
              <div
                className="w-full h-2 bg-gray-200 rounded-md overflow-hidden relative"
                aria-label={`Uploading: ${file.progress || 0}%`}
              >
                <div
                  className="h-full bg-accent2 transition-all duration-300"
                  style={{ width: `${file.progress || 0}%` }}
                />
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Loader2 className="h-4 w-4 text-accent2 animate-spin" />
                <span className="text-xs text-accent2">
                  {file.progress || 0}%
                </span>
              </div>
            </div>
          )}
          {file.status === "error" && (
            <div className="text-xs text-red-600 mt-1">Upload failed</div>
          )}
        </div>
      </div>

      {/* Modal Preview */}
      <Modal
        open={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        dimmer="blurring"
        size="small"
        closeIcon
        className="rounded-2xl max-w-2xl min-h-[90vh] max-h-[90vh] overflow-y-auto"
      >
        <Modal.Header className="font-semibold text-base p-0 m-0">
          {file.name}
        </Modal.Header>
        <Modal.Content className="p-0 m-0">
          <SemanticImage
            src={file.previewUrl}
            fluid
            loading="lazy"
            className="rounded-lg"
          />
        </Modal.Content>
      </Modal>
    </>
  );
};

export default FilePreview2;
