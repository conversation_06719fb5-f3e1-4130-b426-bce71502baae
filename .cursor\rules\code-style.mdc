---
description: Generated code should follow this code style
globs: *.jsx, *.js
alwaysApply: false
---
- Use 2 spaces for indentation  
- Use single quotes for strings (except to avoid escaping)  
- Avoid semicolons unless necessary for disambiguation  
- Eliminate unused variables  
- Add spaces after keywords  
- Add spaces before function declaration parentheses  
- Always use strict equality (`===`) instead of loose equality (`==`)  
- Space infix operators (`a + b`, `a === b`)  
- Add spaces after commas  
- Keep `else` statements on the same line as closing curly braces  
- Use curly braces for multi-line `if` statements  
- Always handle errors in callbacks (e.g., `err` in `callback(err, data)`)  
- Limit line length to 80 characters  
- Use trailing commas in multiline object/array literals  
