import React, { useState } from "react";
import styles from "./scss/AiBtn.module.scss";
import { useNavigate, useSearchParams } from "react-router-dom";
import { processInvoice } from "../../../services/aiServices";
import { createLoadingToast } from "../../../utils/toastUtils";
import { getErrorMessage } from "../../../utils/apiUtils";
import { useAuth } from "../../../../contexts/AuthContext";
import { motion, AnimatePresence } from "framer-motion";
import { RefreshCcw } from "lucide-react";

/** 0 = New (Run AI Extraction), -1 = In progress (Extracting),
 * 1 = Extracted (View Extracted Invoice), -2 = Failed (Retry)
 * */
function getAiBtnStatusBased(
  aiExtractStatusCode,
  handleRunAiExtraction,
  navigate,
  fileId,
  businessObj,
  page
) {
  const baseProps = {
    type: "button",
    className: styles.extractionBtn,
    initial: { y: -5, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 5, opacity: 0 },
    transition: { duration: 0.2 },
  };

  switch (Number(aiExtractStatusCode)) {
    case -1:
      return (
        <motion.button {...baseProps} key="in-progress" disabled>
          Extracting...
        </motion.button>
      );

    case -2:
      return (
        <motion.button
          {...baseProps}
          key="retry"
          className={`${styles.extractionBtn} !bg-red-500 !text-white`}
          onClick={handleRunAiExtraction}
        >
          <span className="flex gap-2 items-center">
            <RefreshCcw size={16} /> Retry
          </span>
        </motion.button>
      );

    case 0:
      return (
        <motion.button
          {...baseProps}
          key="run-extraction"
          onClick={handleRunAiExtraction}
        >
          Run AI Extraction
        </motion.button>
      );

    default:
      return (
        <motion.button
          {...baseProps}
          key="view-extracted"
          onClick={() =>
            navigate(
              `/ai-extraction/f/${fileId}/b/${businessObj?.business_id}/p/${page}`
            )
          }
        >
          View Extracted Invoice
        </motion.button>
      );
  }
}

function AiBtn({ fileId, businessObj, aiExtractStatusCode }) {
  const [currentStatus, setCurrentStatus] = useState(aiExtractStatusCode);
  const { roleType } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const handleRunAiExtraction = () => {
    setCurrentStatus(-1);
    const loadingToast = createLoadingToast("Extracting invoice...");
    processInvoice(fileId, businessObj?.business_id)
      .then(() => {
        setCurrentStatus(1);
        loadingToast("success", "Invoice extracted successfully");
      })
      .catch((err) => {
        setCurrentStatus(-2);
        loadingToast("error", getErrorMessage(err));
      });
  };
  if (roleType !== "accountant") return <></>;
  return (
    <AnimatePresence mode="wait" initial={false}>
      {getAiBtnStatusBased(
        currentStatus,
        handleRunAiExtraction,
        navigate,
        fileId,
        businessObj,
        searchParams?.get("page") || 1
      )}
    </AnimatePresence>
  );
}

export default AiBtn;
