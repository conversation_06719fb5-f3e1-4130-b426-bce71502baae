//environment variables
export const protocol = process.env.REACT_APP_PROTOCOL;
export const domain = process.env.REACT_APP_WS_URL;
export const port = process.env.REACT_APP_PORT;
export const reactport = process.env.REACT_APP_REACTPORT;
export const reactcontext = process.env.REACT_APP_REACTCONTEXT;
export const apicontext = process.env.REACT_APP_APICONTEXT;
export const cookiedomain = process.env.REACT_APP_COOKIEDOMAIN;
export const restbaseurl = process.env.REACT_APP_RESTBASEURL;
export const cdnurl = process.env.REACT_APP_CDNURL;
export const uploadsContext = process.env.REACT_APP_UPLOADSCONTEXT;
export const contexPath = process.env.REACT_APP_CONTEXPATH;
export const timerRefreshInterval = 120000;
/**
 * Dropdown Options
 */
export const timelineDropdownOptions = [
  {
    text: "Last 7 days",
    value: "last7days",
  },
  {
    text: "Current Month",
    value: "monthToDate",
  },
  {
    text: "Last 30 days",
    value: "last30days",
  },
  {
    text: "Current Quarter",
    value: "quarterToDate",
  },
  {
    text: "Current Year",
    value: "yearToDate",
  },
  {
    text: "Custom Range",
    value: "customDate",
  },
];

/**
 * Sorting Options
 */
export const SortOptions = [
  {
    title: "Due Date",
    options: [
      { text: "Oldest to Latest", value: "earliestToLatest" },
      { text: "Latest to Oldest", value: "latestToEarliest" },
    ],
  },
  {
    title: "Amount",
    options: [
      { text: "Low to High", value: "lowToHigh" },
      { text: "High to Low", value: "highToLow" },
    ],
  },
];

/**
 * Month Names List
 */
export const monthNames = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

export const defaultToastOptions = {
  autoClose: 2000,
  hideProgressBar: true,
  closeButton: true,
  isLoading: false,
};
