@import "../../../assets/scss/main.scss";

.fieldWrapper {
  cursor: default !important;
  /* DatePicker input styling */
  :global(.react-datepicker-wrapper) {
    width: 100%;
    height: 100%;
  }
  :global(.react-datepicker__input-container) {
    input {
      width: 100%;
      background-color: inherit;
      border: none !important;
      &:focus {
        border: none;
        outline: none !important;
      }
    }
  }
}

/* Calendar header styling */
:global(.react-datepicker__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e4e8;
  padding-top: 0.8em;
}

/* Day names styling */
:global(.react-datepicker__day-name) {
  color: #4e5ba6;
  font-weight: 600;
  margin: 0.4em;
  width: 1.9em;
}

/* Day styling */
:global(.react-datepicker__day) {
  margin: 0.4em;
  width: 1.9em;
  height: 1.9em;
  line-height: 1.9em;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background-color: #eaecf5;
  }
}

/* Selected day */
:global(.react-datepicker__day--selected) {
  background-color: #4e5ba6;
  color: white;
  font-weight: 600;

  &:hover {
    background-color: darken(#4e5ba6, 10%);
  }
}

/* Today's date */
:global(.react-datepicker__day--today) {
  font-weight: 600;
  border: 1px solid #4e5ba6;
}

/* Month container */
:global(.react-datepicker__month-container) {
  background-color: white;
  border-radius: 0.5em;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  @include clickable;
}

.customHeaderContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5em 0.75em;
  background-color: #f8f9fa;
  border-radius: 0.5em 0.5em 0 0;

  .navButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5em;
    height: 1.5em;
    border: none;
    padding: 0.5em !important;
    background-color: #4e5ba6;
    color: white;
    border-radius: 0.5em;
    font-size: 1.5em;
    transition: background-color 0.2s ease;
    @include clickable;

    &:hover {
      background-color: darken(#4e5ba6, 10%);
    }
  }

  .selectsContainer {
    display: flex;
    justify-content: center;
    flex: 1;
    gap: 0.5em;
    margin: 0 0.5em;
  }

  .yearSelect,
  .monthSelect {
    padding: 0.3em;
    border: 1px solid #e1e4e8;
    border-radius: 0.5em;
    background-color: white;
    font-size: 1.1em;
    cursor: pointer;
    min-width: 5em;
    text-align: center;
    max-height: 2.5em;

    &:focus {
      border-color: #4e5ba6;
      outline: none;
    }
  }
}
