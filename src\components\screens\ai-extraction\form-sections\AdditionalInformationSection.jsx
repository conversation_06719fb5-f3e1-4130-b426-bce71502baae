import React from "react";
import <PERSON><PERSON>ield from "./components/AiField";
import { getFieldAlertObject } from "../../../../components/utils/aiUtils";

const SECTION = "additional_information";

function AdditionalInformationSection({ formData, isReadOnly, formAction }) {
  const data = formData[SECTION] || {};

  return (
    <div className="form-grid">
      <AiField
        label="Remarks"
        isExactMatch={data?.exact_match?.remarks}
        alertObject={getFieldAlertObject(data, "remarks")}
        Element="textarea"
        name="remarks"
        id="remarks"
        value={data?.remarks ?? ""}
        className="only-1-column"
        onChange={(e) =>
          formAction("FIELD_CHANGE", SECTION, "remarks", e.target.value)
        }
        disabled={isReadOnly}
        rows={4}
      />
    </div>
  );
}

export default AdditionalInformationSection;
