import style from "./scss/filePreview.module.scss";
import React, { useState } from "react";
import { Image, Modal } from "semantic-ui-react";
import { getNonePreviewableFileTypePngIcon } from "../utils";
import { CloseIcon } from "../../assets/svgs";

const FilePreview = ({ preview, handleRemove }) => {
  const [isShowGiantPreview, setIsShowGiantPreview] = useState(false);

  const isNonePreviewable = getNonePreviewableFileTypePngIcon(preview.name);

  return (
    <>
      <div className={style.filePreviewWrapper} key={preview.id}>
        <div
          className={style.imageWrapper}
          onClick={() => setIsShowGiantPreview(!!preview.url)}
        >
          <Image
            src={isNonePreviewable ? isNonePreviewable.icon : preview.url}
          />
        </div>
        <p>{preview.name}</p>
        <div onClick={handleRemove} className={style.closeIconWrapper}>
          <CloseIcon />
        </div>
      </div>
      <Modal
        open={isShowGiantPreview}
        onClose={() => setIsShowGiantPreview(false)}
        dimmer="blurring"
      >
        <Modal.Content>
          <Image src={preview.url} fluid loading="lazy" />
        </Modal.Content>
      </Modal>
    </>
  );
};
export default FilePreview;
