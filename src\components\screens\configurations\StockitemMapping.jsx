
import React, { useEffect, useState } from "react";
import LoadingWrapper from "../../global/components/LoadingWrapper";
import InputSearch from "../../ui-components/InputSearch";
import DownloadBtn from "./component/DownloadBtn";
import UploadBtn from "./component/UploadBtn";
import ConfigTables from "./component/ConfigTables";
import usePaginationWithSearch from "./component/usePaginationWithSearch";
import { useAuth } from "../../../contexts/AuthContext";
import { resturls } from "../../utils/apiurls";
import LayoutWrapper from "../../generic-components/LayoutWrapper";
import ConfigHeader from "./component/ConfigHeader";

const columns = [
  { header: "ID", accessor: (_, index) => index + 1 },
  { header: "Item Name", accessor: "item_name" },
  { header: "HSN/SAC", accessor: "hsn_sac" },
  { header: "GST Rate (%)", accessor: "gst_rate" },
  { header: "Stock Group", accessor: "stock_group_name" },
  { header: "Ledger Name", accessor: "ledger_name" },
  {
    header: "Is Active",
    accessor: (row) => (row.is_active ? "Yes" : "No"),
    accessorKey: "is_active",
  },
  {
    header: "Created At",
    accessor: (row) => new Date(row.created_at).toLocaleString(),
    accessorKey: "created_at",
  },
  {
    header: "Updated At",
    accessor: (row) => new Date(row.updated_at).toLocaleString(),
    accessorKey: "updated_at",
  },
];

function Ledgergroupscreen() {
  const { globSelectedBusiness } = useAuth
    ();
  const [keepLoading, setKeepLoading] = useState(true);
  const [selectedSearchAccessor, setSelectedSearchAccessor] = useState("Item Name");

  const {
    rawResponse,
    loading,
    error,
    PaginationComponent,
    refetch,
    query,
    setQuery,
  } = usePaginationWithSearch({
    url: globSelectedBusiness?.business_id
      ? `${resturls.StockItemsMapping}?business_id=${globSelectedBusiness.business_id}`
      : "",
    pageCount: 10,
    shouldDoInitialFetch: false,
    queryParam: selectedSearchAccessor,
    includeNameParam: false,
  });

  const items = rawResponse?.data || [];

  useEffect(() => {
    if (globSelectedBusiness?.business_id) {
      refetch(false);
    }
  }, [globSelectedBusiness]);

  useEffect(() => {
    if (!loading && !!(rawResponse?.data)) setKeepLoading(false);
  }, [loading]);

  return (
    <LayoutWrapper>
      <ConfigHeader  title="stockitem Mapping"/>
      <section className="bg-white rounded-lg shadow">
        <div className="p-4 border-b border-gray-200 flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div className="flex flex-col md:flex-row gap-3 flex-grow">
            <div className="w-full md:w-72">
              <InputSearch
                wrapperClasses="w-full border-2 bg-accent1-bg border-accent1-border "
                inputClasses="w-full px-4 py-2 border border-gray-300 rounded-md  text-black placeholder:text-gray-600 focus:ring-blue-500 focus:border-blue-500"
                placeholder={`Search by ${selectedSearchAccessor}`}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <DownloadBtn
              downloadUrl={`${resturls.StockItemsMappingDownload}?business_id=${globSelectedBusiness?.business_id}`}
              disabled={!globSelectedBusiness?.business_id}
              disableMessage="Please select a business first"
              onError={(err) => console.error("Download failed", err)}
            />
            <UploadBtn
              uploadUrl={`${resturls.StockItemsMappingUpload}?business_id=${globSelectedBusiness?.business_id}`}
              disabled={!globSelectedBusiness?.business_id}
              disableMessage="Please select a business first"
              accept=".csv"
              uploadLabel="Upload Stock Items Mapping"
              onSuccess={() => refetch()}
            />
          </div>
        </div>

        {globSelectedBusiness?.business_id ? (
          <LoadingWrapper loading={loading || keepLoading} error={error}>
            <ConfigTables columns={columns} data={items} rowKey="id" />
            <div className="p-4 border-t border-gray-200">
              {PaginationComponent}
            </div>
          </LoadingWrapper>
        ) : (
          <div className="flex items-center justify-center py-16 text-gray-500">
            Please select a business to view inventory items.
          </div>
        )}
      </section>
    </LayoutWrapper>
  );
}

export default Ledgergroupscreen;