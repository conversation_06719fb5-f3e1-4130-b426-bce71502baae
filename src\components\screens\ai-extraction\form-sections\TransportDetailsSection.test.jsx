import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import TransportDetailsSection from "./TransportDetailsSection";
import { sectionValidation } from "../../../services/aiServices";
import { removeObjectsFromJson } from "../../../utils/jsonUtils";

jest.mock("../../../services/aiServices", () => ({
  sectionValidation: jest.fn(),
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    fileId: "mock-file-id",
    businessId: "mock-business-id",
  }),
}));

const mockFormData = {
  transport_details: {
    transport_name: null,
    vehicle_details: null,
    lr_details: null,
  },
};

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);

  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case "FIELD_CHANGE":
          updatedSection = { ...prevSection, [field]: value };
          break;

        case "UPDATE_SECTION":
          updatedSection = { ...prevSection, ...value };
          break;

        case "HARD_UPDATE_SECTION":
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <TransportDetailsSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe("TransportDetailsSection", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all fields with correct initial values", () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText("Transport Name")).toHaveValue("");
    expect(screen.getByLabelText("Vehicle Details")).toHaveValue("");
    expect(screen.getByLabelText("Lorry Receipt Details")).toHaveValue("");
  });

  it("shows initial error messages & indicators correctly", async () => {
    const formDataWithErrors = {
      transport_details: {
        ...mockFormData.transport_details,
        error: {
          transport_name: {
            short_message: "Invalid transport name",
            long_message: "Please enter a valid transport name",
          },
          vehicle_details: {
            short_message: "Invalid vehicle details",
            long_message: "Please enter valid vehicle details",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId("errorIcon");
    expect(errorIcons).toHaveLength(2);

    const transportNameTooltip = await screen.findByTestId(
      "tooltip-transport_name"
    );
    await userEvent.hover(transportNameTooltip);
    const transportNameTooltipContent = await screen.findByText(
      "Invalid transport name"
    );
    expect(transportNameTooltipContent).toBeInTheDocument();

    const vehicleDetailsTooltip = screen.getByTestId("tooltip-vehicle_details");
    await userEvent.hover(vehicleDetailsTooltip);
    const vehicleDetailsTooltipContent = await screen.findByText(
      "Invalid vehicle details"
    );
    expect(vehicleDetailsTooltipContent).toBeInTheDocument();
  });

  it("shows initial warning messages & indicators correctly", async () => {
    const formDataWithWarnings = {
      transport_details: {
        ...mockFormData.transport_details,
        warning: {
          transport_name: {
            short_message: "Transport name might be incorrect",
            long_message: "Please verify the transport name",
          },
          lr_details: {
            short_message: "LR details might be incorrect",
            long_message: "Please verify the lorry receipt details",
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId("warningIcon");
    expect(warningIcons).toHaveLength(2);

    const transportNameTooltip = await screen.findByTestId(
      "tooltip-transport_name"
    );
    await userEvent.hover(transportNameTooltip);
    const transportNameTooltipContent = await screen.findByText(
      "Transport name might be incorrect"
    );
    expect(transportNameTooltipContent).toBeInTheDocument();

    const lrDetailsTooltip = await screen.findByTestId("tooltip-lr_details");
    await userEvent.hover(lrDetailsTooltip);
    const lrDetailsTooltipContent = await screen.findByText(
      "LR details might be incorrect"
    );
    expect(lrDetailsTooltipContent).toBeInTheDocument();
  });

  it("shows initial exact match indicators when present", async () => {
    const formDataWithSuccess = {
      transport_details: {
        ...mockFormData.transport_details,
        exact_match: {
          transport_name: true,
          vehicle_details: true,
          lr_details: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId("successIcon");
    expect(successIcons).toHaveLength(3);
  });
});
