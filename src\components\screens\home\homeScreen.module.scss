@import "../../../assets/scss/main.scss";

.homeScreenBg {
  color: $primaryColor;
  background: linear-gradient(to bottom, $accentBgColor1 50%, #f6f8fa 50%);
  @include for_media(mobileScreen) {
    background: unset;
  }
}

.homeScreenContainer {
  max-height: 90vh;
  max-height: 90dvh;
  overflow-y: auto;
  @include hide-scrollbar;
  @include for_media(mobileScreen) {
    height: auto;
  }
}
.welcomeMsg {
  color: $primaryColor;
  margin: 0;
  margin-bottom: 1em;
  font-size: 1.5em !important;
  font-weight: 900;
  @include for_media(mobileScreen) {
    padding: 1em 1em;
    color: $black;
    margin-bottom: 0;
  }
}
