import { SearchIcon } from "lucide-react";

function InputSearch({ wrapperClasses = "", inputClasses = "", ...restProps }) {
  return (
    <div
      className={`flex items-center p-0 px-4 rounded-[10px] shadow-[0_0_2px_2px_#E9EAEB] overflow-hidden ${wrapperClasses}`}
    >
      <SearchIcon className="w-6 h-6 flex-shrink-0" />
      <input
        className={`bg-transparent border-none pl-2 min-w-0 text-[#6b6b6b] text-[1.2em] focus:outline-none focus:shadow-none focus:border-none ${inputClasses}`}
        {...restProps}
      />
    </div>
  );
}

export default InputSearch;
