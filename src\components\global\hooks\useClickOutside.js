import { useEffect, useRef } from "react";

/**
 * Custom hook to detect clicks outside of a specified element
 * @param {Function} handler - Callback function to execute when a click outside occurs
 * @param {boolean} [enabled=true] - Whether the click outside detection is enabled
 * @returns {Object} An object containing the ref to attach to the element
 */
function useClickOutside(handler, enabled = true) {
  const elementRef = useRef(null);

  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event) => {
      if (elementRef.current && !elementRef.current.contains(event.target)) {
        handler();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handler, enabled]);

  return { elementRef };
}

export default useClickOutside;
