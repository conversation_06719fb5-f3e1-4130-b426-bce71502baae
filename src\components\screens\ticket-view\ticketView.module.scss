@import "../../../assets//scss/main.scss";

.ticketViewContent {
  display: flex;
  width: 100%;

  @include for_media(mobileScreen) {
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-bottom: 1.5em;
    overflow-y: auto;
    @include hide-scrollbar;
  }
  .infoAndFormContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 27%;
    min-width: 270px;
    max-height: 83dvh;
    overflow-y: auto;
    gap: 0.7em;
    @include hide-scrollbar;
  }

  .commentsContainer {
    width: 100%;
    height: 100%;
    min-height: 83dvh;
    max-height: 83dvh;
    padding: 0 1.5em !important;
    background-color: $white;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    margin-left: 1em;
    @include for_media(mobileScreen) {
      padding: 1.5em 0 !important;
      margin-left: 0;
    }
    .commentInputContainer {
      width: 100%;
      bottom: 0;
      position: sticky;
      z-index: 100;
    }
  }
}
// only in mobile view
.tabSwitchBtnsContainer {
  @include flex-center;
  .tabSwitchBtns {
    width: 85%;
    height: 10%;
    padding: 2em 0;
    background-color: #e9eaeb;
    border-radius: 10px;
    font-size: $smallSize;
    @include flex-center;
    @include clickable;
    p {
      padding: 0.2em;
      margin: 0 0.5em;
      width: 50%;
      text-align: center;
      cursor: pointer;
    }
    .active {
      background-color: $black;
      color: $white;
      border-radius: 10px;
    }
  }
}
