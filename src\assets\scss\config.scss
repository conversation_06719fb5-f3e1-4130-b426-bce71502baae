/*-------------------
       Fonts
--------------------*/

$secondaryFont        : 'DMSans';
$fontVarient3         : 'Inter';
$primaryFont         : 'DMSans';


$black             : black;
$white             : #FFFFFF;
$black2            : rgb(112 112 112);
$bold              : bold;
$normal            : normal;

/*-------------------
      Base Sizes
--------------------*/

/* This is the single variable that controls them all */
$emSize   : 14px;

/* The size of page text  */
$largeSize   : 16px; //This Variable only need config.less
$fontSize    : 14px;
$smallSize   : 12px; //This Variable only need config.less

/*-------------------
    Brand Colors
--------------------*/
$primaryBgColor      : #ffffff;
$secondaryBgColor    : #F5F7FA;
$primaryColor        : #0B1A30;
$accentColor1        : #4AC2FF;
$accentBgColor1      : #4AC2FF1A; 
$accentBorder1       : #4AC2FF50;
$accentHover1        : #4AC2FF33; 
$accentColor2        : #2186D0;
$accentBorder2       : #2186D080;
$accentHover2        : #1B6CAF;
$borderColor         : #E0E6ED;
$successColor        : #5CD68A;
$errorColor          : #FF6B6B;

/* Legacy colors kept for backward compatibility */
$greenVarient2       : #58B5A6;
$blueBackGround      : #F5F7FA; 

$yellowColor         : #F6D659;
$blueBorder          : #0077ff;

$newVarient  : #D9D9D9;


$redColor1        : #FF6B6B;


// $borderColor: #B8B8B8;



/*-------------------
     Breakpoints
--------------------*/
$mobileBreakpoint             : 480px;
$tabletBreakpoint             : 768px;
$bigTabletBreakpoint          : 992px;
$mediumMonitorBreakpoint      : 1200px;

$media_queries : (
    'mobileScreen'       : #{"only screen and (max-width: #{$mobileBreakpoint})"},
    'tabletScreen'       : #{"screen and (min-width: #{$mobileBreakpoint + 1}) and (max-width: #{$tabletBreakpoint}) "},
    'bigTabletScreen'    : #{"screen and (min-width: #{$tabletBreakpoint + 1}) and (max-width: #{$bigTabletBreakpoint}) "},
    'mediumScreen'       : #{"screen and (min-width: #{$bigTabletBreakpoint + 1}) and (max-width: #{$mediumMonitorBreakpoint}) "},
    'computer'           : #{"only screen and (min-width: #{$mediumMonitorBreakpoint + 1})"}
);

@mixin for_media($breakpoint) {
     @if map-has-key($media_queries, $breakpoint) {
       @media #{map-get($media_queries, $breakpoint)} {
         @content;
       }
     } @else {
       @warn "Breakpoint '#{$breakpoint}' not found in the media queries map!";
     }
   }

$grayColor1: #a7a7a7;
$grayColor4: #a7a7a740;
$grayColor5: #F5F5F5;