import React, { useCallback, useMemo, useState } from "react";
import { useAuth } from "../../../contexts/AuthContext";
import { FileText, Plus, Upload } from "lucide-react";
import { toast } from "react-toastify";
import FilePreview2 from "../../custom-components/FilePreview2";
import uploadService from "../../services/uploadService";
import useDragAndDrop from "../../global/hooks/useDragAndDrop";
import { createTicket } from "../../services/ticketServices";
import { getCategoryIdByName } from "../../utils/helperUtils";
import LayoutWrapper from "../../generic-components/LayoutWrapper";

const purchaseExpenseCategory = await getCategoryIdByName([
  { category: "purchase", subCategory: "invoice" },
  { category: "expense" },
]);

function InvoicesCreation() {
  const [files, setFiles] = useState([]);
  const [invoiceType, setInvoiceType] = useState("");
  const { globSelectedBusiness, userInfo } = useAuth();

  const isAllFilesUploaded = useMemo(() => {
    if (files.length === 0) return false;
    return files.every((file) => file.status === "success");
  }, [files]);

  const handleUploadFile = useCallback(async (file) => {
    setFiles((prev) =>
      prev.map((f) => (f.id === file.id ? { ...f, status: "uploading" } : f))
    );
    uploadService
      .uploadFile("/api/file/", file.file, (progress) => {
        setFiles((prev) =>
          prev.map((f) => (f.id === file.id ? { ...f, progress } : f))
        );
      })
      .then((response) => {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === file.id
              ? { ...f, status: "success", responseId: response.id }
              : f
          )
        );
      })
      .catch(() => {
        setFiles((prev) =>
          prev.map((f) => (f.id === file.id ? { ...f, status: "error" } : f))
        );
      });
  }, []);

  const handleFiles = useCallback(
    (selectedFiles) => {
      const filesWithIds = selectedFiles.reduce((acc, file) => {
        const fileType = file.type;
        if (
          fileType === "application/pdf" ||
          fileType === "image/jpeg" ||
          fileType === "image/png"
        ) {
          const id = crypto.randomUUID();
          const fileObj = {
            file: file,
            type: file.type,
            name: file.name,
            progress: 0,
            id,
            previewUrl: URL.createObjectURL(file),
          };
          acc.push(fileObj);
        }
        return acc;
      }, []);

      if (filesWithIds.length !== selectedFiles.length) {
        toast.error(
          "Only PDF and image files are allowed (removed invalid files)"
        );
      }

      setFiles((prevFiles) => [...prevFiles, ...filesWithIds]);
      filesWithIds.forEach((file) => handleUploadFile(file));
    },
    [handleUploadFile]
  );

  const { isDragActive, eventHandlers } = useDragAndDrop(handleFiles);

  const handleRetryUpload = useCallback(
    (id) => {
      const fileToRetry = files.find((f) => f.id === id);
      if (fileToRetry) {
        handleUploadFile(fileToRetry);
      }
    },
    [files, handleUploadFile]
  );

  const handleSubmit = useCallback(async () => {
    if (files.length === 0 || invoiceType === "") return;
    const data = {
      category:
        invoiceType === "purchase"
          ? purchaseExpenseCategory[0].category
          : purchaseExpenseCategory[1].category,
      sub_category: purchaseExpenseCategory[0].subCategory,
      subject:
        invoiceType === "purchase" ? "Purchase Invoice" : "Expense Invoice",
      business: globSelectedBusiness?.business_id,
      assign_to: userInfo?.userId,
    };

    const results = await Promise.allSettled(
      files.map(async (file) => {
        if (!file?.responseId) {
          return Promise.reject(new Error("Missing response ID"));
        }
        data.attachements = [file.responseId];
        return createTicket(data);
      })
    );

    const failedFiles = [];

    results.forEach((result, index) => {
      const file = files[index];
      if (result.status !== "fulfilled") {
        failedFiles.push(file);
      }
    });

    if (failedFiles.length === 0) {
      toast.success("Invoices created successfully");
      setInvoiceType("");
      setFiles([]);
    } else {
      setFiles(failedFiles);
      toast.error(
        `${failedFiles.length} invoice${
          failedFiles.length > 1 ? "s" : ""
        } failed to submit. Please retry.`
      );
    }
  }, [files, invoiceType, globSelectedBusiness, userInfo?.userId]);

  return (
    <LayoutWrapper>
      <div className="w-full mx-auto p-6 h-dvh overflow-y-auto">
        <h1 className="text-2xl font-bold text-gray-900">
          Create New Invoices
        </h1>

        <div className="flex flex-col items-start w-full gap-2">
          <p className="text-sm text-gray-500 mb-0">
            Only PDF and image files are allowed.
          </p>

          <div
            className={`w-full border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer transition-colors ${
              isDragActive ? "border-accent1 bg-accent1-bg" : "border-gray-300"
            }`}
            {...eventHandlers}
          >
            <div className="mb-4 bg-gray-50 p-3 rounded-full">
              <div className="relative">
                <FileText className="h-10 w-10 text-accent2" />
                <div className="absolute bottom-0 right-0 bg-accent2 rounded-full p-1">
                  <Plus className="h-3 w-3 text-white" />
                </div>
              </div>
            </div>

            <p className="text-gray-800 font-medium mb-4 select-none">
              Drag and drop file here, or
            </p>

            <label className="bg-accent2 hover:bg-accent2-hover text-secondary-color font-medium py-2 px-6 rounded-full flex items-center cursor-pointer select-none">
              <Upload className="h-5 w-5 mr-2" />
              Upload
              <input
                type="file"
                className="hidden"
                multiple
                accept=".pdf, .png, .jpg, .jpeg, application/pdf, image/png, image/jpeg"
                onChange={(e) => handleFiles(Array.from(e.target.files))}
              />
            </label>
          </div>

          <InvoiceTypeSelector
            invoiceType={invoiceType}
            setInvoiceType={setInvoiceType}
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
          {files.map((file) => (
            <FilePreview2
              key={file.id}
              file={file}
              onRemove={() =>
                setFiles((prev) => prev.filter((f) => f.id !== file.id))
              }
              onRetry={() => handleRetryUpload(file.id)}
            />
          ))}
        </div>
        <button
          data-tooltip-id="tooltip"
          data-tooltip-content={
            invoiceType === "" ? "Please select an invoice type" : ""
          }
          onClick={handleSubmit}
          disabled={!isAllFilesUploaded || invoiceType === ""}
          className={`px-6 py-2 rounded-md font-medium ${
            !isAllFilesUploaded || invoiceType === ""
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : "bg-accent2 text-white hover:bg-accent2-hover"
          }`}
        >
          Submit
        </button>
      </div>
    </LayoutWrapper>
  );
}

function InvoiceTypeSelector({ invoiceType, setInvoiceType }) {
  return (
    <div className="flex items-center gap-4">
      <h2 className="text-lg font-semibold text-primary-color m-0">
        Invoice Type <span className="text-error">*</span>
      </h2>
      <label className="flex items-center">
        <input
          type="radio"
          name="invoiceType"
          value="purchase"
          checked={invoiceType === "purchase"}
          onChange={() => setInvoiceType("purchase")}
          className="mr-2"
        />
        Purchase
      </label>
      <label className="flex items-center">
        <input
          type="radio"
          name="invoiceType"
          value="expense"
          checked={invoiceType === "expense"}
          onChange={() => setInvoiceType("expense")}
          className="mr-2"
        />
        Expense
      </label>
    </div>
  );
}

export default InvoicesCreation;
