import { getCategories } from "../services/userServices";

export function isEmpty(value) {
  return (
    value === undefined ||
    value === null ||
    (typeof value === "string" && value.trim() === "") ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === "object" &&
      value !== null &&
      !Array.isArray(value) &&
      Object.keys(value).length === 0) ||
    (typeof value === "number" && isNaN(value))
  );
}

/**
 * Converts category and subcategory names to their respective IDs.
 *
 * @param {{ category: string, subCategory?: string }[]} inputArray
 * @returns {Promise<{ category: number | null, subCategory: number | null }[]>}
 */
export const getCategoryIdByName = async (inputArray) => {
  try {
    const data = await getCategories();

    return inputArray.map(({ category, subCategory }) => {
      let categoryId = null;
      let subCategoryId = null;

      const categoryObj = data.results.find(
        (c) => c.name.toLowerCase() === category.toLowerCase()
      );

      if (categoryObj) {
        categoryId = categoryObj.id;

        if (subCategory) {
          const subObj = categoryObj.subcategories.find(
            (s) => s.name.toLowerCase() === subCategory.toLowerCase()
          );
          if (subObj) {
            subCategoryId = subObj.id;
          }
        }
      }

      return {
        category: categoryId,
        subCategory: subCategory ? subCategoryId : null,
      };
    });
  } catch (error) {
    console.error("Error resolving category/subcategory IDs:", error);
    return inputArray.map(() => ({ category: null, subCategory: null }));
  }
};
