import React from "react";
import { Loader } from "semantic-ui-react";
import style from "./scss/loadingWrapper.module.scss";
import Empty from "./Empty";

function LoadingWrapper({
  children,
  loading,
  error,
  LoaderWrapperClass,
  isRenderEmpty = false,
  doNotRenderChildIf = false,
  renderEmpty = null,
  minHeight = false,
  useBlankSkeleton = false,
  ...loaderProps
}) {
  if (loading) {
    return useBlankSkeleton ? (
      <div className="blankSkeleton" />
    ) : (
      <div
        className={`${minHeight && style.standardHeight} ${
          LoaderWrapperClass || style.loaderWrapper
        }`}
      >
        <Loader active {...loaderProps} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-full m-0">
        <p className="text-red-600">Error: {String(error)}</p>
        <div
          className={
            "bg-blue-950 text-white text-lg py-2 px-5 rounded-lg cursor-pointer select-none"
          }
          onClick={() => window.location.reload()}
        >
          Retry
        </div>
      </div>
    );
  }

  if (renderEmpty && isRenderEmpty) {
    return (
      <Empty title={renderEmpty.title} description={renderEmpty.description} />
    );
  }

  if (!doNotRenderChildIf) {
    return children;
  }

  return <></>;
}

export default LoadingWrapper;
